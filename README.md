# 🤖 GPT OSS Translator

Lok<PERSON>lny prekladač textov pomocou GPT OSS modelu cez Ollama.

## 📋 Obsah

- [Inštalácia](#inštalácia)
- [Použitie](#použitie)
- [Aplikácie](#aplikácie)
- [R<PERSON>š<PERSON>e problémov](#riešenie-problémov)

## 🔧 Inštalácia

### 1. Inštalácia Ollama

```bash
# macOS (Homebrew)
brew install ollama

# Spustenie Ollama servera
ollama serve
```

### 2. Stiahnutie GPT OSS modelu

```bash
# Stiahnutie modelu (13 GB)
ollama pull gpt-oss:20b
```

### 3. <PERSON>

```bash
# Základné závislosti
pip install requests

# Pre DeepSeek model (voliteľné)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install transformers sentencepiece bitsandbytes
```

## 🚀 Použitie

### Multi-Model GUI Aplikácia (Odporúčané) ⭐

Spustenie pokročilej aplikácie s podporou viacerých modelov:

```bash
./run_multi_translator.sh
```

**Funkcie:**
- 🤖 **Dva modely:** GPT OSS (Ollama) + DeepSeek-7B
- 🔄 **Prepínač modelov:** Jednoduché prepínanie medzi modelmi
- 📊 **Štatistiky:** Počítadlo prekladov, celkový a priemerný čas
- ⏱️ **Presné meranie:** Čas každého prekladu
- 🌍 **6 jazykov:** slovenčina, čeština, nemčina, francúzština, španielčina, taliančina
- ✨ **Moderné GUI:** Prehľadné a intuitívne rozhranie
- 🚀 **Optimalizácia:** Automatické načítanie modelov podľa potreby

### Základná GUI Aplikácia

Spustenie jednoduchej aplikácie (iba GPT OSS):

```bash
./run_translator.sh
```

**Funkcie:**
- ✨ Moderné grafické rozhranie
- 🌍 Výber z 6 jazykov
- ⏱️ Zobrazenie času prekladu
- 🔄 Automatická kontrola dostupnosti servera
- 📝 Veľké textové polia pre dlhšie texty

### Konzolová aplikácia

Spustenie jednoduchej konzolowej aplikácie:

```bash
python3 simple_translator.py
```

**Funkcie:**
- 💻 Jednoduché textové rozhranie
- 🔄 Interaktívny výber jazyka
- ⚡ Rýchle testovanie prekladov

### Programové použitie

```python
from gpt_oss_translator import GPTOSSTranslator

# Vytvorenie prekladača
translator = GPTOSSTranslator()

# Kontrola dostupnosti
if translator.check_server_status() and translator.check_model_availability():
    # Preklad textu
    result = translator.translate_subtitle("Hello, world!")
    print(f"Preklad: {result}")
```

## 📱 Aplikácie

### 1. `multi_model_translator.py` - **NOVÁ** Multi-Model GUI Aplikácia ⭐
- **Účel:** Pokročilá aplikácia s podporou GPT OSS a DeepSeek modelov
- **Výhody:**
  - 🔄 Prepínanie medzi modelmi
  - 📊 Počítadlo prekladov a štatistiky
  - ⏱️ Meranie času prekladov
  - 🎯 Optimalizované pre rýchlosť
- **Spustenie:** `./run_multi_translator.sh`
- **Použitie:** Profesionálny preklad s výberom najlepšieho modelu

### 2. `translator_app.py` - Základná GUI Aplikácia
- **Účel:** Jednoduchá aplikácia s grafickým rozhraním (iba GPT OSS)
- **Výhody:** Najjednoduchšie na použitie, vizuálne príjemné
- **Spustenie:** `./run_translator.sh`
- **Použitie:** Každodenný preklad textov

### 3. `simple_translator.py` - Konzolová aplikácia
- **Účel:** Rýchle testovanie a preklad cez terminál
- **Výhody:** Rýchle spustenie, minimálne systémové požiadavky
- **Použitie:** Testovanie, automatizácia

### 4. `gpt_oss_translator.py` - Knižnica
- **Účel:** Programové API pre integráciu do iných projektov
- **Výhody:** Flexibilné, rozšíriteľné
- **Použitie:** Integrácia do vlastných aplikácií

### 5. Testovacie skripty
- `quick_test.py` - Rýchly test funkčnosti
- `extended_test.py` - Rozšírené testovanie s viacerými príkladmi
- `example_usage.py` - Príklady použitia API

## ⚡ Výkon

### GPT OSS (Ollama) - Očakávané časy:
- **Krátke vety (1-5 slov):** 20-40 sekúnd
- **Stredné vety (6-15 slov):** 25-45 sekúnd
- **Dlhé vety (16+ slov):** 30-60 sekúnd

### DeepSeek-7B - Očakávané časy:
- **Krátke vety (1-5 slov):** 1-3 sekundy ⚡
- **Stredné vety (6-15 slov):** 2-5 sekúnd ⚡
- **Dlhé vety (16+ slov):** 3-8 sekúnd ⚡

### Optimalizácia:
- **GPT OSS:** Prvý preklad po spustení je najpomalší (načítanie modelu)
- **DeepSeek:** Prvé načítanie trvá 2-5 minút, potom je veľmi rýchly
- **Tip:** Pre rýchle preklady použite DeepSeek, pre kvalitu GPT OSS
- Kratšie texty sa prekladajú rýchlejšie

## 🌍 Podporované jazyky

1. **Slovenčina** (predvolený)
2. **Čeština**
3. **Nemčina** 
4. **Francúzština**
5. **Španielčina**
6. **Taliančina**

## 🔧 Riešenie problémov

### ❌ "Ollama server nie je dostupný"

**Riešenie:**
```bash
# Spustite Ollama server
ollama serve
```

### ❌ "Model gpt-oss:20b nie je dostupný"

**Riešenie:**
```bash
# Stiahnutie modelu
ollama pull gpt-oss:20b

# Kontrola nainštalovaných modelov
ollama list
```

### ⏰ "Timeout - preklad trval príliš dlho"

**Riešenie:**
- Prvý preklad po spustení trvá dlhšie (načítanie modelu)
- Skúste kratší text
- Reštartujte Ollama server: `ollama serve`

### 🐌 Pomalé preklady

**Optimalizácia:**
- Používajte kratšie vety
- Reštartujte Ollama server
- Skontrolujte dostupnú RAM (model potrebuje ~8-16 GB)

### 📝 Prázdne preklady

**Riešenie:**
- Skúste iný prompt alebo kratší text
- Reštartujte Ollama server
- Skontrolujte, či model správne funguje: `ollama run gpt-oss:20b "Hello"`

## 📊 Systémové požiadavky

- **RAM:** Minimálne 16 GB (odporúčané 32 GB)
- **Disk:** 15 GB voľného miesta pre model
- **Python:** 3.7+
- **OS:** macOS, Linux, Windows (s WSL)

## 🆘 Podpora

Ak máte problémy:

1. **Skontrolujte status servera:**
   ```bash
   curl http://localhost:11434/api/tags
   ```

2. **Reštartujte Ollama:**
   ```bash
   # Ukončite existujúci server (Ctrl+C)
   ollama serve
   ```

3. **Spustite test:**
   ```bash
   python3 quick_test.py
   ```

## 📝 Poznámky

- Model je optimalizovaný pre krátke texty (titulky, vety)
- Prvé spustenie po reštarte je pomalšie
- Kvalita prekladu závisí od kontextu a dĺžky textu
- Pre najlepšie výsledky používajte jasné, jednoduché vety
