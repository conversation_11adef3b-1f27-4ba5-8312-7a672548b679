#!/usr/bin/env python3
"""
R<PERSON><PERSON>ly test GPT OSS Translator
"""

from gpt_oss_translator import GPTOSSTranslator
import time

def quick_test():
    """Rýchly test základnej funk<PERSON>"""
    print("🚀 Rýchly test GPT OSS Translator")
    print("=" * 50)
    
    translator = GPTOSSTranslator()
    
    # Kontrola dostupnosti
    print("🔍 Kontrolujem server a model...")
    if not translator.check_server_status():
        print("❌ Ollama server nie je dostupný!")
        return
    
    if not translator.check_model_availability():
        print("❌ Model gpt-oss:20b nie je dostupný!")
        return
    
    print("✅ Server a model sú dostupné")
    
    # Test s jednoduchou vetou
    test_text = "Hello"
    print(f"\n📝 Testujem preklad: '{test_text}'")
    print("⏳ Čakám na odpoveď (môže trvať 1-2 minúty pri prvom spustení)...")
    
    start_time = time.time()
    result = translator.translate_subtitle(test_text)
    end_time = time.time()
    
    duration = end_time - start_time
    
    if result:
        print(f"✅ Úspech! Preklad: '{result}'")
        print(f"⏱️  Čas: {duration:.2f} sekúnd")
        
        # Test rýchlosti pre krátky text
        if duration < 60:
            print("🚀 Rýchlosť je prijateľná pre krátke texty")
        else:
            print("⚠️  Preklad je pomalý, ale funkčný")
    else:
        print(f"❌ Preklad neúspešný po {duration:.2f} sekundách")

if __name__ == "__main__":
    quick_test()
