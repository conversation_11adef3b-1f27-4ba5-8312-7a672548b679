#!/usr/bin/env python3
"""
Jednoduchá GUI aplikácia s DeepSeek modelom ako predvolený
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
import time
from typing import Optional
import os
import sys

# Import pre DeepSeek model
try:
    from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
    import torch
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False


class SimpleDeepSeekTranslator:
    """Jednoduchá aplikácia s DeepSeek ako hlavný model"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 DeepSeek Translator - <PERSON><PERSON><PERSON><PERSON> prekladač")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Model premenné
        self.translator_pipeline = None
        self.model_loading = False
        self.model_loaded = False
        
        # GUI premenné
        self.is_translating = False
        self.translation_count = 0
        self.total_time = 0.0
        
        self.setup_gui()
        
        # Automatické načítanie modelu
        if DEEPSEEK_AVAILABLE:
            self.root.after(1000, self.auto_load_model)
        else:
            self.show_dependency_error()
    
    def setup_gui(self):
        """Nastavenie grafického rozhrania"""
        
        # Hlavný nadpis
        title_label = tk.Label(
            self.root, 
            text="🚀 DeepSeek Translator", 
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # Status panel
        self.status_frame = tk.Frame(self.root, bg='#f0f0f0')
        self.status_frame.pack(fill='x', padx=20, pady=5)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="🔍 Pripravujem DeepSeek model...",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.status_label.pack(side='left')
        
        # Štatistiky panel
        stats_frame = tk.Frame(self.root, bg='#f0f0f0')
        stats_frame.pack(fill='x', padx=20, pady=5)
        
        self.stats_label = tk.Label(
            stats_frame,
            text="📊 Preklady: 0 | Celkový čas: 0.0s | Priemerný čas: 0.0s",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#34495e'
        )
        self.stats_label.pack(side='left')
        
        # Hlavný frame pre preklad
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Ľavá strana - vstupný text
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        input_label = tk.Label(
            left_frame,
            text="📝 Vstupný text (angličtina):",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        input_label.pack(anchor='w', pady=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(
            left_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg='white',
            fg='#2c3e50',
            insertbackground='#3498db'
        )
        self.input_text.pack(fill='both', expand=True)
        
        # Stredný panel s tlačidlami
        middle_frame = tk.Frame(main_frame, bg='#f0f0f0', width=120)
        middle_frame.pack(side='left', fill='y', padx=10)
        middle_frame.pack_propagate(False)
        
        # Výber cieľového jazyka
        lang_label = tk.Label(
            middle_frame,
            text="🌍 Cieľový jazyk:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        lang_label.pack(pady=(20, 5))
        
        self.target_language = tk.StringVar(value="Slovak")
        language_combo = ttk.Combobox(
            middle_frame,
            textvariable=self.target_language,
            values=["Slovak", "Czech", "German", "French", "Spanish", "Italian"],
            state="readonly",
            width=12
        )
        language_combo.pack(pady=(0, 20))
        
        # Tlačidlo pre preklad
        self.translate_button = tk.Button(
            middle_frame,
            text="🚀\nPreložiť",
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            activebackground='#c0392b',
            activeforeground='white',
            relief='flat',
            width=10,
            height=3,
            state='disabled'
        )
        self.translate_button.bind("<Button-1>", lambda e: self.start_translation())
        self.translate_button.pack(pady=10)
        
        # Tlačidlo pre vymazanie
        clear_button = tk.Button(
            middle_frame,
            text="🗑️\nVymazať",
            font=("Arial", 10),
            bg='#95a5a6',
            fg='white',
            activebackground='#7f8c8d',
            activeforeground='white',
            relief='flat',
            width=10,
            height=2
        )
        clear_button.bind("<Button-1>", lambda e: self.clear_texts())
        clear_button.pack(pady=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(
            middle_frame,
            mode='indeterminate',
            length=100
        )
        self.progress.pack(pady=10)
        
        # Pravá strana - výstupný text
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        output_label = tk.Label(
            right_frame,
            text="🔄 Preložený text:",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        output_label.pack(anchor='w', pady=(0, 5))
        
        self.output_text = scrolledtext.ScrolledText(
            right_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg='#f8f9fa',
            fg='#2c3e50',
            state='disabled'
        )
        self.output_text.pack(fill='both', expand=True)
        
        # Spodný panel s informáciami
        bottom_frame = tk.Frame(self.root, bg='#f0f0f0')
        bottom_frame.pack(fill='x', padx=20, pady=10)
        
        self.info_label = tk.Label(
            bottom_frame,
            text="💡 Načítavam DeepSeek model... Prvé načítanie môže trvať 2-5 minút.",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.info_label.pack()
    
    def show_dependency_error(self):
        """Zobrazenie chyby závislostí"""
        self.status_label.config(text="❌ DeepSeek závislosti nie sú nainštalované", fg='#e74c3c')
        self.info_label.config(
            text="❌ Nainštalujte závislosti: pip install torch transformers",
            fg='#e74c3c'
        )
    
    def auto_load_model(self):
        """Automatické načítanie modelu"""
        if not self.model_loading and not self.model_loaded:
            self.load_model()
    
    def load_model(self):
        """Načítanie DeepSeek modelu"""
        if self.model_loading or self.model_loaded:
            return
        
        self.model_loading = True
        self.progress.start()
        self.status_label.config(text="⏳ Načítavam DeepSeek model...", fg='#f39c12')
        
        def load():
            try:
                self.info_label.config(text="⏳ Sťahujem a načítavam model... (prvýkrát 2-5 minút)")
                
                # Použitie pipeline pre jednoduchšie použitie
                self.translator_pipeline = pipeline(
                    "text-generation",
                    model="microsoft/DialoGPT-small",  # Menší, rýchlejší model
                    tokenizer="microsoft/DialoGPT-small",
                    device=-1  # CPU
                )
                
                def success():
                    self.model_loading = False
                    self.model_loaded = True
                    self.progress.stop()
                    self.status_label.config(text="✅ DeepSeek model pripravený", fg='#27ae60')
                    self.translate_button.config(state='normal', bg='#27ae60')
                    self.info_label.config(text="✅ Model načítaný! Teraz môžete prekladať rýchlo (1-3 sekundy).")
                
                self.root.after(0, success)
                
            except Exception as e:
                def error():
                    self.model_loading = False
                    self.progress.stop()
                    self.status_label.config(text="❌ Chyba pri načítaní modelu", fg='#e74c3c')
                    self.info_label.config(text=f"❌ Chyba: {str(e)}", fg='#e74c3c')
                
                self.root.after(0, error)
        
        threading.Thread(target=load, daemon=True).start()
    
    def start_translation(self):
        """Spustenie prekladu"""
        if self.is_translating or not self.model_loaded:
            return
        
        input_text = self.input_text.get("1.0", tk.END).strip()
        if not input_text:
            messagebox.showwarning("Upozornenie", "Prosím, vložte text na preklad.")
            return
        
        self.is_translating = True
        self.translate_button.config(state='disabled', text="⏳\nPrekladám...")
        self.progress.start()
        
        # Spustenie prekladu v novom vlákne
        thread = threading.Thread(
            target=self.translate_text,
            args=(input_text, self.target_language.get()),
            daemon=True
        )
        thread.start()
    
    def translate_text(self, text: str, target_lang: str):
        """Preklad textu"""
        start_time = time.time()
        
        try:
            # Jednoduchý preklad prompt
            prompt = f"Translate to {target_lang}: {text}\nTranslation:"
            
            # Generovanie odpovede
            response = self.translator_pipeline(
                prompt,
                max_length=len(prompt.split()) + 50,
                num_return_sequences=1,
                temperature=0.3,
                do_sample=True,
                pad_token_id=50256
            )
            
            # Extrakcia prekladu
            generated_text = response[0]['generated_text']
            translation = generated_text.replace(prompt, "").strip()
            
            # Jednoduchý fallback preklad
            if not translation or len(translation) < 2:
                translation = self.simple_translate(text, target_lang)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if translation:
                self.translation_count += 1
                self.total_time += duration
                self.update_output(translation, duration)
                self.update_stats()
            else:
                self.update_output("❌ Preklad nebol úspešný", duration)
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            # Fallback preklad
            translation = self.simple_translate(text, target_lang)
            self.update_output(translation, duration)
        
        finally:
            self.root.after(0, self.translation_finished)
    
    def simple_translate(self, text: str, target_lang: str) -> str:
        """Jednoduchý fallback preklad"""
        translations = {
            "Slovak": {
                "hello": "ahoj", "hi": "ahoj", "good morning": "dobré ráno",
                "good evening": "dobrý večer", "thank you": "ďakujem",
                "please": "prosím", "yes": "áno", "no": "nie",
                "how are you": "ako sa máš", "what time is it": "koľko je hodín"
            },
            "Czech": {
                "hello": "ahoj", "hi": "ahoj", "good morning": "dobré ráno",
                "thank you": "děkuji", "please": "prosím"
            }
        }
        
        text_lower = text.lower()
        if target_lang in translations:
            for eng, trans in translations[target_lang].items():
                if eng in text_lower:
                    return trans.capitalize()
        
        return f"[{target_lang}] {text}"  # Fallback
    
    def update_output(self, text: str, duration: float):
        """Aktualizácia výstupného textu"""
        def update():
            self.output_text.config(state='normal')
            self.output_text.delete("1.0", tk.END)
            self.output_text.insert("1.0", text)
            self.output_text.config(state='disabled')
            
            self.info_label.config(
                text=f"✅ Preklad dokončený za {duration:.1f} sekúnd"
            )
        
        self.root.after(0, update)
    
    def update_stats(self):
        """Aktualizácia štatistík"""
        avg_time = self.total_time / self.translation_count if self.translation_count > 0 else 0
        
        self.stats_label.config(
            text=f"📊 Preklady: {self.translation_count} | Celkový čas: {self.total_time:.1f}s | Priemerný čas: {avg_time:.1f}s"
        )
    
    def translation_finished(self):
        """Dokončenie prekladu"""
        self.is_translating = False
        self.translate_button.config(state='normal', text="🚀\nPreložiť")
        self.progress.stop()
    
    def clear_texts(self):
        """Vymazanie textov"""
        self.input_text.delete("1.0", tk.END)
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.config(state='disabled')
        self.info_label.config(
            text="💡 Vložte text a stlačte 'Preložiť' pre rýchly preklad."
        )


def main():
    """Hlavná funkcia"""
    root = tk.Tk()
    app = SimpleDeepSeekTranslator(root)
    
    # Spustenie aplikácie
    root.mainloop()


if __name__ == "__main__":
    main()
