#!/usr/bin/env python3
"""
Jednoduchá konzolová aplikácia pre preklad textov pomocou lokálneho GPT OSS modelu
"""

import requests
import json
import time
import sys
from typing import Optional


class SimpleTranslator:
    """Jednoduchá trieda pre preklad"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "gpt-oss:20b"
    
    def check_server(self) -> bool:
        """Kontrola dostupnosti servera a modelu"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return any(model['name'] == self.model_name for model in models)
            return False
        except:
            return False
    
    def translate(self, text: str, target_language: str = "slovenčina") -> Optional[str]:
        """Preklad textu"""
        prompt = f"Translate the following text to {target_language}. Respond only with the translation:\n\n{text}\n\nTranslation:"
        
        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,
                "num_predict": 200,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1
            }
        }
        
        try:
            print("⏳ Prekladám... (môže trvať 30-60 sekúnd)")
            start_time = time.time()
            
            response = requests.post(self.ollama_url, json=payload, timeout=120)
            
            if response.status_code == 200:
                result = response.json()
                translated_text = result.get('response', '').strip()
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"⏱️  Čas prekladu: {duration:.1f} sekúnd")
                return translated_text
            else:
                print(f"❌ Chyba API: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            print("⏰ Timeout - preklad trval príliš dlho")
            return None
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return None


def main():
    """Hlavná funkcia pre konzolové rozhranie"""
    print("🤖 GPT OSS Simple Translator")
    print("=" * 50)
    
    translator = SimpleTranslator()
    
    # Kontrola servera
    print("🔍 Kontrolujem server a model...")
    if not translator.check_server():
        print("❌ Ollama server alebo model gpt-oss:20b nie je dostupný!")
        print("💡 Uistite sa, že:")
        print("   1. Ollama server beží: ollama serve")
        print("   2. Model je stiahnutý: ollama pull gpt-oss:20b")
        return
    
    print("✅ Server a model sú pripravené")
    print()
    
    # Dostupné jazyky
    languages = {
        "1": "slovenčina",
        "2": "čeština", 
        "3": "nemčina",
        "4": "francúzština",
        "5": "španielčina",
        "6": "taliančina"
    }
    
    while True:
        print("\n" + "=" * 50)
        print("📝 NOVÝ PREKLAD")
        print("=" * 50)
        
        # Vstup textu
        print("Vložte text na preklad (alebo 'quit' pre ukončenie):")
        text = input("📝 Text: ").strip()
        
        if text.lower() in ['quit', 'exit', 'q']:
            print("👋 Ďakujem za použitie!")
            break
        
        if not text:
            print("⚠️  Prázdny text, skúste znovu.")
            continue
        
        # Výber jazyka
        print("\nVyberte cieľový jazyk:")
        for key, lang in languages.items():
            print(f"  {key}. {lang}")
        
        lang_choice = input("🌍 Jazyk (1-6, alebo Enter pre slovenčinu): ").strip()
        
        if lang_choice == "":
            target_lang = "slovenčina"
        elif lang_choice in languages:
            target_lang = languages[lang_choice]
        else:
            print("⚠️  Neplatný výber, používam slovenčinu")
            target_lang = "slovenčina"
        
        print(f"\n📋 Prekladám do jazyka: {target_lang}")
        print(f"📝 Originál: '{text}'")
        
        # Preklad
        result = translator.translate(text, target_lang)
        
        if result:
            print(f"✅ Preklad: '{result}'")
        else:
            print("❌ Preklad nebol úspešný")
        
        # Pokračovanie
        print("\nChcete preložiť ďalší text? (y/n)")
        continue_choice = input("🔄 Pokračovať: ").strip().lower()
        
        if continue_choice in ['n', 'no', 'nie']:
            print("👋 Ďakujem za použitie!")
            break


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Aplikácia ukončená používateľom")
        sys.exit(0)
