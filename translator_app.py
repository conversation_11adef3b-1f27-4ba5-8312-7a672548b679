#!/usr/bin/env python3
"""
GUI aplikácia pre preklad textov pomocou lokálneho GPT OSS modelu
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
import time
from typing import Optional


class TranslatorApp:
    """Hlavná trieda pre prekladovú aplikáciu"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("GPT OSS Translator - Lokálny prekladač")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Nastavenia pre Ollama
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "gpt-oss:20b"
        
        # Premenné pre GUI
        self.is_translating = False
        
        self.setup_gui()
        self.check_server_status()
    
    def setup_gui(self):
        """Nastavenie grafického rozhrania"""
        
        # Hlavný nadpis
        title_label = tk.Label(
            self.root, 
            text="🤖 GPT OSS Translator", 
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # Status panel
        self.status_frame = tk.Frame(self.root, bg='#f0f0f0')
        self.status_frame.pack(fill='x', padx=20, pady=5)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="🔍 Kontrolujem server...",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.status_label.pack(side='left')
        
        # Hlavný frame pre preklad
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Ľavá strana - vstupný text
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        input_label = tk.Label(
            left_frame,
            text="📝 Vstupný text (angličtina):",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        input_label.pack(anchor='w', pady=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(
            left_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg='white',
            fg='#2c3e50',
            insertbackground='#3498db'
        )
        self.input_text.pack(fill='both', expand=True)
        
        # Stredný panel s tlačidlami
        middle_frame = tk.Frame(main_frame, bg='#f0f0f0', width=120)
        middle_frame.pack(side='left', fill='y', padx=10)
        middle_frame.pack_propagate(False)
        
        # Výber cieľového jazyka
        lang_label = tk.Label(
            middle_frame,
            text="🌍 Cieľový jazyk:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        lang_label.pack(pady=(20, 5))
        
        self.target_language = tk.StringVar(value="slovenčina")
        language_combo = ttk.Combobox(
            middle_frame,
            textvariable=self.target_language,
            values=["slovenčina", "čeština", "nemčina", "francúzština", "španielčina", "taliančina"],
            state="readonly",
            width=12
        )
        language_combo.pack(pady=(0, 20))
        
        # Tlačidlo pre preklad
        self.translate_button = tk.Button(
            middle_frame,
            text="🔄\nPreložiť",
            font=("Arial", 12, "bold"),
            bg='#3498db',
            fg='white',
            activebackground='#2980b9',
            activeforeground='white',
            relief='flat',
            width=10,
            height=3,
            command=self.start_translation
        )
        self.translate_button.pack(pady=10)
        
        # Tlačidlo pre vymazanie
        clear_button = tk.Button(
            middle_frame,
            text="🗑️\nVymazať",
            font=("Arial", 10),
            bg='#e74c3c',
            fg='white',
            activebackground='#c0392b',
            activeforeground='white',
            relief='flat',
            width=10,
            height=2,
            command=self.clear_texts
        )
        clear_button.pack(pady=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(
            middle_frame,
            mode='indeterminate',
            length=100
        )
        self.progress.pack(pady=10)
        
        # Pravá strana - výstupný text
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        output_label = tk.Label(
            right_frame,
            text="🔄 Preložený text:",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        output_label.pack(anchor='w', pady=(0, 5))
        
        self.output_text = scrolledtext.ScrolledText(
            right_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg='#f8f9fa',
            fg='#2c3e50',
            state='disabled'
        )
        self.output_text.pack(fill='both', expand=True)
        
        # Spodný panel s informáciami
        bottom_frame = tk.Frame(self.root, bg='#f0f0f0')
        bottom_frame.pack(fill='x', padx=20, pady=10)
        
        self.info_label = tk.Label(
            bottom_frame,
            text="💡 Tip: Vložte text a stlačte 'Preložiť'. Prvý preklad môže trvať dlhšie.",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.info_label.pack()
    
    def check_server_status(self):
        """Kontrola stavu Ollama servera"""
        def check():
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    if any(model['name'] == self.model_name for model in models):
                        self.status_label.config(
                            text="✅ Server a model sú pripravené",
                            fg='#27ae60'
                        )
                        self.translate_button.config(state='normal')
                    else:
                        self.status_label.config(
                            text="❌ Model gpt-oss:20b nie je dostupný",
                            fg='#e74c3c'
                        )
                        self.translate_button.config(state='disabled')
                else:
                    self.status_label.config(
                        text="❌ Server nie je dostupný",
                        fg='#e74c3c'
                    )
                    self.translate_button.config(state='disabled')
            except Exception:
                self.status_label.config(
                    text="❌ Ollama server nie je spustený",
                    fg='#e74c3c'
                )
                self.translate_button.config(state='disabled')
        
        threading.Thread(target=check, daemon=True).start()
    
    def start_translation(self):
        """Spustenie prekladu v samostatnom vlákne"""
        if self.is_translating:
            return
        
        input_text = self.input_text.get("1.0", tk.END).strip()
        if not input_text:
            messagebox.showwarning("Upozornenie", "Prosím, vložte text na preklad.")
            return
        
        self.is_translating = True
        self.translate_button.config(state='disabled', text="⏳\nPrekladám...")
        self.progress.start()
        
        # Spustenie prekladu v novom vlákne
        thread = threading.Thread(
            target=self.translate_text,
            args=(input_text, self.target_language.get()),
            daemon=True
        )
        thread.start()
    
    def translate_text(self, text: str, target_lang: str):
        """Preklad textu pomocou Ollama API"""
        try:
            # Vytvorenie promptu
            prompt = f"Translate the following text to {target_lang}. Respond only with the translation:\n\n{text}\n\nTranslation:"
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "num_predict": 200,
                    "top_p": 0.9,
                    "top_k": 40,
                    "repeat_penalty": 1.1
                }
            }
            
            start_time = time.time()
            response = requests.post(self.ollama_url, json=payload, timeout=120)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                translated_text = result.get('response', '').strip()
                
                if translated_text:
                    duration = end_time - start_time
                    self.update_output(translated_text, duration)
                else:
                    self.update_output("❌ Model vrátil prázdnu odpoveď", 0)
            else:
                self.update_output(f"❌ Chyba API: {response.status_code}", 0)
                
        except requests.exceptions.Timeout:
            self.update_output("⏰ Timeout - preklad trval príliš dlho", 0)
        except Exception as e:
            self.update_output(f"❌ Chyba: {str(e)}", 0)
        
        finally:
            self.root.after(0, self.translation_finished)
    
    def update_output(self, text: str, duration: float):
        """Aktualizácia výstupného textu"""
        def update():
            self.output_text.config(state='normal')
            self.output_text.delete("1.0", tk.END)
            self.output_text.insert("1.0", text)
            self.output_text.config(state='disabled')
            
            if duration > 0:
                self.info_label.config(
                    text=f"✅ Preklad dokončený za {duration:.1f} sekúnd"
                )
            else:
                self.info_label.config(
                    text="❌ Preklad nebol úspešný"
                )
        
        self.root.after(0, update)
    
    def translation_finished(self):
        """Dokončenie prekladu"""
        self.is_translating = False
        self.translate_button.config(state='normal', text="🔄\nPreložiť")
        self.progress.stop()
    
    def clear_texts(self):
        """Vymazanie textov"""
        self.input_text.delete("1.0", tk.END)
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.config(state='disabled')
        self.info_label.config(
            text="💡 Tip: Vložte text a stlačte 'Preložiť'. Prvý preklad môže trvať dlhšie."
        )


def main():
    """Hlavná funkcia"""
    root = tk.Tk()
    app = TranslatorApp(root)
    
    # Nastavenie ikony a ďalších vlastností okna
    try:
        root.iconbitmap()  # Môžete pridať cestu k ikone
    except:
        pass
    
    # Spustenie aplikácie
    root.mainloop()


if __name__ == "__main__":
    main()
