#!/usr/bin/env python3
"""
Príklad použitia GPT OSS Translator pre preklad titulkov
"""

from gpt_oss_translator import GPTOSSTranslator
import time


def example_basic_translation():
    """Základný príklad prekladu"""
    print("=" * 60)
    print("🔤 ZÁKLADNÝ PREKLAD")
    print("=" * 60)
    
    translator = GPTOSSTranslator()
    
    # Kontrola dostupnosti
    if not translator.check_server_status():
        print("❌ Ollama server nie je dostupný!")
        return
    
    if not translator.check_model_availability():
        print("❌ Model gpt-oss:20b nie je dostupný!")
        return
    
    # Preklad jednoduchej vety
    text = "Hello, how are you today?"
    print(f"📝 Prekladám: '{text}'")
    
    translated = translator.translate_subtitle(text)
    if translated:
        print(f"✅ Výsledok: '{translated}'")
    else:
        print("❌ Preklad sa nepodaril")


def example_subtitle_translation():
    """Príklad prekladu titulkov"""
    print("\n" + "=" * 60)
    print("🎬 PREKLAD TITULKOV")
    print("=" * 60)
    
    translator = GPTOSSTranslator()
    
    # Simulácia titulkov z filmu/seriálu
    subtitles = [
        "Welcome to our show!",
        "Today we'll learn about artificial intelligence.",
        "Machine learning is fascinating.",
        "Let's start with the basics.",
        "Thank you for watching!"
    ]
    
    print("🎭 Prekladám titulky...")
    
    translated_subtitles = []
    for i, subtitle in enumerate(subtitles, 1):
        print(f"\n📺 Titulok {i}/{len(subtitles)}")
        translated = translator.translate_subtitle(subtitle)
        translated_subtitles.append(translated)
    
    print("\n📋 SÚHRN PREKLADOV:")
    print("-" * 40)
    for original, translated in zip(subtitles, translated_subtitles):
        status = "✅" if translated else "❌"
        print(f"{status} '{original}'")
        print(f"   → '{translated or 'CHYBA'}'")
        print()


def example_batch_translation():
    """Príklad hromadného prekladu"""
    print("\n" + "=" * 60)
    print("📦 HROMADNÝ PREKLAD")
    print("=" * 60)
    
    translator = GPTOSSTranslator()
    
    # Rôzne typy textov
    texts = [
        "Good morning!",
        "How can I help you?",
        "Please wait a moment.",
        "The meeting starts at 3 PM.",
        "Have a great weekend!"
    ]
    
    print("🚀 Spúšťam hromadný preklad...")
    start_time = time.time()
    
    results = translator.batch_translate(texts)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️  Celkový čas: {total_time:.2f}s")
    print(f"📊 Priemerný čas na preklad: {total_time/len(texts):.2f}s")
    
    # Štatistiky
    successful = sum(1 for result in results.values() if result is not None)
    failed = len(results) - successful
    
    print(f"✅ Úspešné preklady: {successful}/{len(texts)}")
    print(f"❌ Neúspešné preklady: {failed}/{len(texts)}")


def example_different_languages():
    """Príklad prekladu do rôznych jazykov"""
    print("\n" + "=" * 60)
    print("🌍 PREKLAD DO RÔZNYCH JAZYKOV")
    print("=" * 60)
    
    translator = GPTOSSTranslator()
    
    text = "Hello, welcome to our application!"
    languages = [
        "slovenčina",
        "čeština", 
        "nemčina",
        "francúzština",
        "španielčina"
    ]
    
    print(f"📝 Originálny text: '{text}'")
    print("\n🌐 Preklady:")
    
    for language in languages:
        print(f"\n🔄 Prekladám do jazyka: {language}")
        translated = translator.translate_text(text, target_language=language)
        if translated:
            print(f"✅ {language}: '{translated}'")
        else:
            print(f"❌ {language}: CHYBA")


def example_performance_test():
    """Test výkonu prekladu"""
    print("\n" + "=" * 60)
    print("⚡ TEST VÝKONU")
    print("=" * 60)
    
    translator = GPTOSSTranslator()
    
    # Test s rôzne dlhými textmi
    test_cases = [
        ("Krátky", "Hi!"),
        ("Stredný", "Hello, how are you doing today?"),
        ("Dlhý", "Good morning! I hope you're having a wonderful day. The weather is beautiful and I'm looking forward to our meeting this afternoon.")
    ]
    
    print("🏃‍♂️ Testujem rýchlosť prekladu...")
    
    for case_name, text in test_cases:
        print(f"\n📏 {case_name} text ({len(text)} znakov):")
        print(f"   '{text}'")
        
        start_time = time.time()
        translated = translator.translate_subtitle(text)
        end_time = time.time()
        
        duration = end_time - start_time
        chars_per_second = len(text) / duration if duration > 0 else 0
        
        if translated:
            print(f"✅ Preložené za {duration:.2f}s ({chars_per_second:.1f} znakov/s)")
            print(f"   → '{translated}'")
        else:
            print(f"❌ Preklad neúspešný za {duration:.2f}s")


def main():
    """Hlavná funkcia s príkladmi použitia"""
    print("🤖 GPT OSS Translator - Príklady použitia")
    print("=" * 60)
    
    # Spustenie všetkých príkladov
    try:
        example_basic_translation()
        example_subtitle_translation()
        example_batch_translation()
        example_different_languages()
        example_performance_test()
        
        print("\n" + "=" * 60)
        print("🎉 Všetky príklady dokončené!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️  Prerušené používateľom")
    except Exception as e:
        print(f"\n💥 Neočakávaná chyba: {e}")


if __name__ == "__main__":
    main()
