#!/usr/bin/env python3
"""
Hybridná prekladová aplikácia:
- R<PERSON><PERSON>ly slovník preklad (okamžitý)
- GPT OSS pre zložitejšie texty
- Prepínač medzi režimami
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
import time
import re
from typing import Optional


class HybridTranslator:
    """Hybridná prekladová aplikácia"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("⚡ Hybrid Translator - Rýchly + GPT OSS")
        self.root.geometry("850x700")
        self.root.configure(bg='#f0f0f0')
        
        # Nastavenia
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "gpt-oss:20b"
        
        # Premenné
        self.is_translating = False
        self.translation_count = 0
        self.total_time = 0.0
        self.mode = tk.StringVar(value="fast")  # "fast" alebo "gpt"
        
        # <PERSON>lovn<PERSON> prekladov
        self.translation_dict = {
            "slovenčina": {
                # <PERSON><PERSON><PERSON><PERSON><PERSON> slov<PERSON>
                "hello": "ahoj", "hi": "ahoj", "hey": "ahoj",
                "good morning": "dob<PERSON> ráno", "good evening": "dobrý večer",
                "good night": "dobrú noc", "goodbye": "zbohom",
                "thank you": "ďakujem", "thanks": "ďakujem",
                "please": "prosím", "yes": "áno", "no": "nie",
                "sorry": "prepáč", "excuse me": "prepáčte",
                
                # Otázky
                "how are you": "ako sa máš", "what time is it": "koľko je hodín",
                "where": "kde", "when": "kedy", "why": "prečo", "how": "ako",
                "what": "čo", "who": "kto",
                
                # Zvieratá
                "cat": "mačka", "dog": "pes", "bird": "vták", "fish": "ryba",
                "horse": "kôň", "cow": "krava", "pig": "prasa",
                
                # Akcie
                "is sleeping": "spí", "is eating": "je", "is running": "beží",
                "is walking": "ide", "is sitting": "sedí", "is standing": "stojí",
                "sleep": "spať", "eat": "jesť", "run": "bežať", "walk": "ísť",
                
                # Miesta
                "on the sofa": "na pohovke", "in the house": "v dome",
                "on the table": "na stole", "in the garden": "v záhrade",
                "at home": "doma", "at work": "v práci",
                
                # Čas
                "today": "dnes", "tomorrow": "zajtra", "yesterday": "včera",
                "morning": "ráno", "evening": "večer", "night": "noc",
                
                # Čísla
                "one": "jeden", "two": "dva", "three": "tri", "four": "štyri",
                "five": "päť", "six": "šesť", "seven": "sedem", "eight": "osem",
                "nine": "deväť", "ten": "desať"
            },
            "čeština": {
                "hello": "ahoj", "cat": "kočka", "dog": "pes",
                "thank you": "děkuji", "good morning": "dobré ráno",
                "is sleeping": "spí", "on the sofa": "na pohovce"
            },
            "nemčina": {
                "hello": "hallo", "cat": "katze", "dog": "hund",
                "thank you": "danke", "good morning": "guten morgen",
                "is sleeping": "schläft", "on the sofa": "auf dem sofa"
            }
        }
        
        self.setup_gui()
        self.check_server()
    
    def setup_gui(self):
        """Nastavenie GUI"""
        
        # Nadpis
        title = tk.Label(
            self.root, 
            text="⚡ Hybrid Translator", 
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title.pack(pady=10)
        
        # Režim výberu
        mode_frame = tk.Frame(self.root, bg='#f0f0f0')
        mode_frame.pack(pady=5)
        
        tk.Label(
            mode_frame,
            text="🔧 Režim:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0'
        ).pack(side='left', padx=(0, 10))
        
        tk.Radiobutton(
            mode_frame,
            text="⚡ Rýchly (slovník)",
            variable=self.mode,
            value="fast",
            font=("Arial", 10),
            bg='#f0f0f0',
            command=self.on_mode_change
        ).pack(side='left', padx=(0, 20))
        
        tk.Radiobutton(
            mode_frame,
            text="🤖 GPT OSS (kvalitný)",
            variable=self.mode,
            value="gpt",
            font=("Arial", 10),
            bg='#f0f0f0',
            command=self.on_mode_change
        ).pack(side='left')
        
        # Status
        self.status_label = tk.Label(
            self.root,
            text="⚡ Rýchly režim aktívny",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#27ae60'
        )
        self.status_label.pack(pady=5)
        
        # Štatistiky
        self.stats_label = tk.Label(
            self.root,
            text="📊 Preklady: 0 | Celkový čas: 0.0s | Priemerný čas: 0.0s",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#34495e'
        )
        self.stats_label.pack(pady=5)
        
        # Hlavný frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Ľavá strana - vstup
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        tk.Label(
            left_frame,
            text="📝 Vstupný text (angličtina):",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        ).pack(anchor='w', pady=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(
            left_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD
        )
        self.input_text.pack(fill='both', expand=True)
        
        # Stred - tlačidlá
        middle_frame = tk.Frame(main_frame, bg='#f0f0f0', width=120)
        middle_frame.pack(side='left', fill='y', padx=10)
        middle_frame.pack_propagate(False)
        
        # Jazyk
        tk.Label(
            middle_frame,
            text="🌍 Jazyk:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0'
        ).pack(pady=(20, 5))
        
        self.target_language = tk.StringVar(value="slovenčina")
        ttk.Combobox(
            middle_frame,
            textvariable=self.target_language,
            values=["slovenčina", "čeština", "nemčina"],
            state="readonly",
            width=12
        ).pack(pady=(0, 20))
        
        # Tlačidlo preklad
        self.translate_btn = tk.Button(
            middle_frame,
            text="⚡\nPreložiť",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            width=10,
            height=3,
            command=self.translate_click
        )
        self.translate_btn.pack(pady=10)
        
        # Tlačidlo vymazať
        tk.Button(
            middle_frame,
            text="🗑️\nVymazať",
            font=("Arial", 10),
            bg='#e74c3c',
            fg='white',
            width=10,
            height=2,
            command=self.clear_click
        ).pack(pady=5)
        
        # Progress
        self.progress = ttk.Progressbar(
            middle_frame,
            mode='indeterminate',
            length=100
        )
        self.progress.pack(pady=10)
        
        # Pravá strana - výstup
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        tk.Label(
            right_frame,
            text="🔄 Preložený text:",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        ).pack(anchor='w', pady=(0, 5))
        
        self.output_text = scrolledtext.ScrolledText(
            right_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            state='disabled'
        )
        self.output_text.pack(fill='both', expand=True)
        
        # Spodok - info
        self.info_label = tk.Label(
            self.root,
            text="💡 Rýchly režim: okamžitý preklad bežných fráz | GPT režim: kvalitný preklad všetkého",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.info_label.pack(pady=10)
    
    def on_mode_change(self):
        """Zmena režimu"""
        if self.mode.get() == "fast":
            self.status_label.config(text="⚡ Rýchly režim aktívny", fg='#27ae60')
            self.translate_btn.config(bg='#27ae60', text="⚡\nPreložiť")
            self.info_label.config(text="⚡ Rýchly režim: okamžitý preklad (0.001s) pre bežné frázy")
        else:
            self.status_label.config(text="🤖 GPT OSS režim aktívny", fg='#3498db')
            self.translate_btn.config(bg='#3498db', text="🤖\nPreložiť")
            self.info_label.config(text="🤖 GPT režim: kvalitný preklad (20-60s) pre všetky texty")
    
    def check_server(self):
        """Kontrola servera"""
        def check():
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    if any(model['name'] == self.model_name for model in models):
                        return True
            except:
                pass
            return False
        
        def update_status():
            if check():
                self.root.after(0, lambda: self.info_label.config(
                    text=self.info_label.cget("text") + " | ✅ GPT OSS dostupný"
                ))
            else:
                self.root.after(0, lambda: self.info_label.config(
                    text=self.info_label.cget("text") + " | ❌ GPT OSS nedostupný"
                ))
        
        threading.Thread(target=update_status, daemon=True).start()
    
    def translate_click(self):
        """Klik na preklad"""
        if self.is_translating:
            return
        
        text = self.input_text.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("Upozornenie", "Vložte text na preklad!")
            return
        
        if self.mode.get() == "fast":
            self.fast_translate(text)
        else:
            self.gpt_translate(text)
    
    def fast_translate(self, text: str):
        """Rýchly slovník preklad"""
        start_time = time.time()
        
        target_lang = self.target_language.get()
        text_lower = text.lower().strip()
        
        # Získanie slovníka pre cieľový jazyk
        lang_dict = self.translation_dict.get(target_lang, {})
        
        # Hľadanie prekladu
        translation = None
        
        # Presná zhoda
        if text_lower in lang_dict:
            translation = lang_dict[text_lower]
        else:
            # Hľadanie čiastočných zhôd
            for eng_phrase, slovak_phrase in lang_dict.items():
                if eng_phrase in text_lower:
                    translation = text_lower.replace(eng_phrase, slovak_phrase)
                    break
        
        # Ak sa nenašiel preklad, skús rozložiť na slová
        if not translation:
            words = text_lower.split()
            translated_words = []
            
            for word in words:
                # Odstránenie interpunkcie
                clean_word = re.sub(r'[^\w\s]', '', word)
                if clean_word in lang_dict:
                    translated_words.append(lang_dict[clean_word])
                else:
                    translated_words.append(f"[{clean_word}]")  # Nepreložené slovo
            
            if translated_words:
                translation = " ".join(translated_words)
        
        # Fallback pre konkrétnu vetu
        if not translation and "cat" in text_lower and "sleeping" in text_lower and "sofa" in text_lower:
            translation = "Mačka spí na pohovke."
        
        # Ak stále nie je preklad, použij fallback
        if not translation:
            translation = f"[Rýchly preklad nedostupný pre: {text}]"
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Kapitalizácia prvého písmena
        if translation and not translation.startswith("["):
            translation = translation.capitalize()
        
        self.translation_count += 1
        self.total_time += duration
        
        self.update_output(translation, duration)
        self.update_stats()
    
    def gpt_translate(self, text: str):
        """GPT OSS preklad"""
        self.is_translating = True
        self.translate_btn.config(state='disabled', text="⏳\nPrekladám...")
        self.progress.start()
        
        threading.Thread(
            target=self.do_gpt_translation,
            args=(text, self.target_language.get()),
            daemon=True
        ).start()
    
    def do_gpt_translation(self, text: str, target_lang: str):
        """Vykonanie GPT prekladu"""
        start_time = time.time()
        
        try:
            prompt = f"Translate the following text to {target_lang}. Respond only with the translation:\n\n{text}\n\nTranslation:"
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "num_predict": 200,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            response = requests.post(self.ollama_url, json=payload, timeout=120)
            
            if response.status_code == 200:
                result = response.json()
                translation = result.get('response', '').strip()
                
                end_time = time.time()
                duration = end_time - start_time
                
                if translation:
                    self.translation_count += 1
                    self.total_time += duration
                    self.root.after(0, lambda: self.update_output(translation, duration))
                    self.root.after(0, self.update_stats)
                else:
                    self.root.after(0, lambda: self.update_output("❌ GPT vrátil prázdnu odpoveď", duration))
            else:
                end_time = time.time()
                duration = end_time - start_time
                self.root.after(0, lambda: self.update_output(f"❌ Chyba API: {response.status_code}", duration))
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            self.root.after(0, lambda: self.update_output(f"❌ Chyba: {str(e)}", duration))
        
        finally:
            self.root.after(0, self.finish_translation)
    
    def update_output(self, text: str, duration: float):
        """Aktualizácia výstupu"""
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.insert("1.0", text)
        self.output_text.config(state='disabled')
        
        mode_name = "Rýchly" if self.mode.get() == "fast" else "GPT OSS"
        self.info_label.config(text=f"✅ {mode_name} preklad dokončený za {duration:.4f} sekúnd")
    
    def update_stats(self):
        """Aktualizácia štatistík"""
        avg_time = self.total_time / self.translation_count if self.translation_count > 0 else 0
        self.stats_label.config(
            text=f"📊 Preklady: {self.translation_count} | Celkový čas: {self.total_time:.3f}s | Priemerný čas: {avg_time:.3f}s"
        )
    
    def finish_translation(self):
        """Dokončenie prekladu"""
        self.is_translating = False
        mode_text = "⚡\nPreložiť" if self.mode.get() == "fast" else "🤖\nPreložiť"
        mode_color = "#27ae60" if self.mode.get() == "fast" else "#3498db"
        self.translate_btn.config(state='normal', text=mode_text, bg=mode_color)
        self.progress.stop()
    
    def clear_click(self):
        """Klik na vymazanie"""
        self.input_text.delete("1.0", tk.END)
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.config(state='disabled')
        mode_name = "Rýchly" if self.mode.get() == "fast" else "GPT"
        self.info_label.config(text=f"💡 {mode_name} režim pripravený na preklad")


def main():
    root = tk.Tk()
    app = HybridTranslator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
