#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> test GPT OSS Translator s viacer<PERSON><PERSON> príkladmi
"""

from gpt_oss_translator import GPTOSSTranslator
import time

def extended_test():
    """Roz<PERSON><PERSON>ren<PERSON> test s viacerými príkladmi"""
    print("🧪 Rozšírený test GPT OSS Translator")
    print("=" * 60)
    
    translator = GPTOSSTranslator()
    
    # Kontrola dostupnosti
    if not translator.check_server_status() or not translator.check_model_availability():
        print("❌ Server alebo model nie je dostupný!")
        return
    
    print("✅ Server a model sú pripravené")
    
    # Testovacie vety rôznych dĺžok
    test_cases = [
        ("<PERSON>r<PERSON>tka", "Hi!"),
        ("<PERSON>red<PERSON>", "How are you?"),
        ("Dl<PERSON>š<PERSON>", "Good morning, have a nice day!"),
        ("Ot<PERSON><PERSON><PERSON>", "What time is it?"),
        ("Poďakovanie", "Thank you very much!")
    ]
    
    print(f"\n📋 Testujem {len(test_cases)} príkladov...")
    print("-" * 60)
    
    results = []
    total_time = 0
    
    for i, (category, text) in enumerate(test_cases, 1):
        print(f"\n🔄 Test {i}/{len(test_cases)} - {category}")
        print(f"📝 Originál: '{text}'")
        
        start_time = time.time()
        translated = translator.translate_subtitle(text)
        end_time = time.time()
        
        duration = end_time - start_time
        total_time += duration
        
        if translated:
            print(f"✅ Preklad: '{translated}'")
            print(f"⏱️  Čas: {duration:.1f}s")
            results.append((text, translated, duration, True))
        else:
            print(f"❌ Preklad neúspešný")
            print(f"⏱️  Čas: {duration:.1f}s")
            results.append((text, None, duration, False))
        
        print("-" * 40)
    
    # Súhrn výsledkov
    print(f"\n📊 SÚHRN TESTOV")
    print("=" * 60)
    
    successful = sum(1 for _, _, _, success in results if success)
    failed = len(results) - successful
    avg_time = total_time / len(results) if results else 0
    
    print(f"✅ Úspešné preklady: {successful}/{len(results)}")
    print(f"❌ Neúspešné preklady: {failed}/{len(results)}")
    print(f"⏱️  Celkový čas: {total_time:.1f}s")
    print(f"📈 Priemerný čas: {avg_time:.1f}s")
    
    if successful > 0:
        successful_times = [duration for _, _, duration, success in results if success]
        fastest = min(successful_times)
        slowest = max(successful_times)
        print(f"🚀 Najrýchlejší preklad: {fastest:.1f}s")
        print(f"🐌 Najpomalší preklad: {slowest:.1f}s")
    
    print("\n📋 DETAILNÉ VÝSLEDKY:")
    print("-" * 60)
    for original, translated, duration, success in results:
        status = "✅" if success else "❌"
        print(f"{status} '{original}' → '{translated or 'CHYBA'}' ({duration:.1f}s)")
    
    # Odporúčania
    print(f"\n💡 ODPORÚČANIA:")
    print("-" * 60)
    if avg_time < 30:
        print("🚀 Rýchlosť je výborná pre krátke titulky")
    elif avg_time < 60:
        print("⚡ Rýchlosť je prijateľná pre bežné použitie")
    else:
        print("⚠️  Rýchlosť je pomalá, zvážte optimalizáciu")
    
    if successful == len(results):
        print("🎉 Všetky preklady boli úspešné!")
    elif successful > len(results) * 0.8:
        print("👍 Väčšina prekladov bola úspešná")
    else:
        print("⚠️  Mnoho prekladov zlyhalo, skontrolujte nastavenia")

if __name__ == "__main__":
    extended_test()
