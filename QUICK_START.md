# 🚀 Quick Start Guide

## Najrýchle<PERSON><PERSON><PERSON> spôsob spustenia

### 1. Multi-Model Translator (Odporúčané) ⭐

```bash
cd /Users/<USER>/Desktop/data_analitics/projects/chatgpt
./run_multi_translator.sh
```

**Výhody:**
- 🤖 **Dva modely:** GPT OSS + DeepSeek-7B
- ⚡ **DeepSeek je 10x rýchlejší** (1-8 sekúnd vs 20-60 sekúnd)
- 📊 **Štatistiky:** Počítadlo prekladov a časov
- 🔄 **Prepínač modelov** v GUI

### 2. Základný Translator

```bash
cd /Users/<USER>/Desktop/data_analitics/projects/chatgpt
./run_translator.sh
```

## 🎯 Ktorý model vybrať?

| Model | Rýchlosť | Kvalita | Použitie |
|-------|----------|---------|----------|
| **DeepSeek-7B** | ⚡⚡⚡ (1-8s) | ⭐⭐⭐⭐ | Rý<PERSON>le preklady, titulky |
| **GPT OSS** | 🐌 (20-60s) | ⭐⭐⭐⭐⭐ | Kvalitné preklady, dlhé texty |

## 📋 Kroky na prvé spustenie

1. **Spustite Multi-Model Translator:**
   ```bash
   ./run_multi_translator.sh
   ```

2. **Vyberte model:**
   - Pre rýchlosť: `DeepSeek-7B` (stlačte "Načítať DeepSeek")
   - Pre kvalitu: `GPT OSS` (už je pripravený)

3. **Vložte text a preložte!**

## 🔧 Riešenie problémov

### ❌ "Ollama server nie je dostupný"
```bash
ollama serve
```

### ❌ "DeepSeek závislosti nie sú nainštalované"
```bash
/Library/Frameworks/Python.framework/Versions/3.10/bin/python3 -m pip install torch transformers bitsandbytes
```

### ⚠️ "DeepSeek model nie je načítaný"
- Stlačte tlačidlo "Načítať DeepSeek" v aplikácii
- Prvé načítanie trvá 2-5 minút
- Potom je model veľmi rýchly!

## 📊 Očakávané výsledky

### DeepSeek-7B (Rýchly):
- "Hello" → "Ahoj" (2 sekundy)
- "How are you?" → "Ako sa máš?" (3 sekundy)
- "Good morning!" → "Dobré ráno!" (2 sekundy)

### GPT OSS (Kvalitný):
- "Hello" → "Ahoj" (25 sekúnd)
- "How are you?" → "Ako sa máš?" (30 sekúnd)
- "Good morning!" → "Dobré ráno!" (28 sekúnd)

## 🎉 Hotovo!

Teraz máte k dispozícii pokročilý prekladač s dvoma modelmi a možnosťou výberu podľa vašich potrieb!
