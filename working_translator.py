#!/usr/bin/env python3
"""
Funkčná GUI aplikácia pre preklad - bez problémov s tlačidlami
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
import time
from typing import Optional


class WorkingTranslator:
    """Jednoduchá funkčná aplikácia"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 Working Translator - GPT OSS + Počítadlo")
        self.root.geometry("800x650")
        self.root.configure(bg='#f0f0f0')
        
        # Nastavenia
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "gpt-oss:20b"
        
        # Premenné
        self.is_translating = False
        self.translation_count = 0
        self.total_time = 0.0
        
        self.setup_gui()
        self.check_server()
    
    def setup_gui(self):
        """Nastavenie GUI"""
        
        # Nadpis
        title = tk.Label(
            self.root, 
            text="🚀 Working Translator", 
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title.pack(pady=10)
        
        # Status
        self.status_label = tk.Label(
            self.root,
            text="🔍 Kontrolujem server...",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.status_label.pack(pady=5)
        
        # Štatistiky
        self.stats_label = tk.Label(
            self.root,
            text="📊 Preklady: 0 | Celkový čas: 0.0s | Priemerný čas: 0.0s",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#34495e'
        )
        self.stats_label.pack(pady=5)
        
        # Hlavný frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Ľavá strana - vstup
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        tk.Label(
            left_frame,
            text="📝 Vstupný text (angličtina):",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        ).pack(anchor='w', pady=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(
            left_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD
        )
        self.input_text.pack(fill='both', expand=True)
        
        # Stred - tlačidlá
        middle_frame = tk.Frame(main_frame, bg='#f0f0f0', width=120)
        middle_frame.pack(side='left', fill='y', padx=10)
        middle_frame.pack_propagate(False)
        
        # Jazyk
        tk.Label(
            middle_frame,
            text="🌍 Jazyk:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0'
        ).pack(pady=(20, 5))
        
        self.target_language = tk.StringVar(value="slovenčina")
        ttk.Combobox(
            middle_frame,
            textvariable=self.target_language,
            values=["slovenčina", "čeština", "nemčina", "francúzština"],
            state="readonly",
            width=12
        ).pack(pady=(0, 20))
        
        # Tlačidlo preklad
        self.translate_btn = tk.Button(
            middle_frame,
            text="🔄\nPreložiť",
            font=("Arial", 12, "bold"),
            bg='#3498db',
            fg='white',
            width=10,
            height=3,
            command=self.translate_click
        )
        self.translate_btn.pack(pady=10)
        
        # Tlačidlo vymazať
        tk.Button(
            middle_frame,
            text="🗑️\nVymazať",
            font=("Arial", 10),
            bg='#e74c3c',
            fg='white',
            width=10,
            height=2,
            command=self.clear_click
        ).pack(pady=5)
        
        # Progress
        self.progress = ttk.Progressbar(
            middle_frame,
            mode='indeterminate',
            length=100
        )
        self.progress.pack(pady=10)
        
        # Pravá strana - výstup
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        tk.Label(
            right_frame,
            text="🔄 Preložený text:",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        ).pack(anchor='w', pady=(0, 5))
        
        self.output_text = scrolledtext.ScrolledText(
            right_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            state='disabled'
        )
        self.output_text.pack(fill='both', expand=True)
        
        # Spodok - info
        self.info_label = tk.Label(
            self.root,
            text="💡 Vložte text a stlačte 'Preložiť'",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.info_label.pack(pady=10)
    
    def check_server(self):
        """Kontrola servera"""
        def check():
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    if any(model['name'] == self.model_name for model in models):
                        self.root.after(0, lambda: self.status_label.config(
                            text="✅ GPT OSS model pripravený", fg='#27ae60'
                        ))
                        self.root.after(0, lambda: self.translate_btn.config(state='normal'))
                        return
                
                self.root.after(0, lambda: self.status_label.config(
                    text="❌ GPT OSS model nie je dostupný", fg='#e74c3c'
                ))
                self.root.after(0, lambda: self.translate_btn.config(state='disabled'))
                
            except:
                self.root.after(0, lambda: self.status_label.config(
                    text="❌ Ollama server nie je spustený", fg='#e74c3c'
                ))
                self.root.after(0, lambda: self.translate_btn.config(state='disabled'))
        
        threading.Thread(target=check, daemon=True).start()
    
    def translate_click(self):
        """Klik na preklad"""
        if self.is_translating:
            return
        
        text = self.input_text.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("Upozornenie", "Vložte text na preklad!")
            return
        
        self.is_translating = True
        self.translate_btn.config(state='disabled', text="⏳\nPrekladám...")
        self.progress.start()
        
        # Spustenie v novom vlákne
        threading.Thread(
            target=self.do_translation,
            args=(text, self.target_language.get()),
            daemon=True
        ).start()
    
    def do_translation(self, text: str, target_lang: str):
        """Vykonanie prekladu"""
        start_time = time.time()
        
        try:
            prompt = f"Translate the following text to {target_lang}. Respond only with the translation:\n\n{text}\n\nTranslation:"
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "num_predict": 200,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            response = requests.post(self.ollama_url, json=payload, timeout=120)
            
            if response.status_code == 200:
                result = response.json()
                translation = result.get('response', '').strip()
                
                end_time = time.time()
                duration = end_time - start_time
                
                if translation:
                    self.translation_count += 1
                    self.total_time += duration
                    
                    # Aktualizácia GUI
                    self.root.after(0, lambda: self.update_output(translation, duration))
                    self.root.after(0, self.update_stats)
                else:
                    self.root.after(0, lambda: self.update_output("❌ Prázdna odpoveď", duration))
            else:
                end_time = time.time()
                duration = end_time - start_time
                self.root.after(0, lambda: self.update_output(f"❌ Chyba API: {response.status_code}", duration))
                
        except requests.exceptions.Timeout:
            end_time = time.time()
            duration = end_time - start_time
            self.root.after(0, lambda: self.update_output("⏰ Timeout - preklad trval príliš dlho", duration))
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            self.root.after(0, lambda: self.update_output(f"❌ Chyba: {str(e)}", duration))
        
        finally:
            self.root.after(0, self.finish_translation)
    
    def update_output(self, text: str, duration: float):
        """Aktualizácia výstupu"""
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.insert("1.0", text)
        self.output_text.config(state='disabled')
        
        self.info_label.config(text=f"✅ Preklad dokončený za {duration:.1f} sekúnd")
    
    def update_stats(self):
        """Aktualizácia štatistík"""
        avg_time = self.total_time / self.translation_count if self.translation_count > 0 else 0
        self.stats_label.config(
            text=f"📊 Preklady: {self.translation_count} | Celkový čas: {self.total_time:.1f}s | Priemerný čas: {avg_time:.1f}s"
        )
    
    def finish_translation(self):
        """Dokončenie prekladu"""
        self.is_translating = False
        self.translate_btn.config(state='normal', text="🔄\nPreložiť")
        self.progress.stop()
    
    def clear_click(self):
        """Klik na vymazanie"""
        self.input_text.delete("1.0", tk.END)
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.config(state='disabled')
        self.info_label.config(text="💡 Vložte text a stlačte 'Preložiť'")


def main():
    root = tk.Tk()
    app = WorkingTranslator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
