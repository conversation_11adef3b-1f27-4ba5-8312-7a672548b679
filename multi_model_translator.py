#!/usr/bin/env python3
"""
Multi-model GUI aplikácia pre preklad textov
Podporuje GPT OSS (cez Ollama) a DeepSeek modely
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
import time
from typing import Optional
import os
import sys

# Import pre DeepSeek model
try:
    from transformers import AutoModelForCausalLM, AutoTokenizer
    import torch
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False


class MultiModelTranslator:
    """Hlavná trieda pre multi-model prekladovú aplikáciu"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🤖 Multi-Model Translator - GPT OSS & DeepSeek")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # Nastavenia pre modely
        self.ollama_url = "http://localhost:11434/api/generate"
        self.gpt_model_name = "gpt-oss:20b"
        
        # DeepSeek model premenné
        self.deepseek_model = None
        self.deepseek_tokenizer = None
        self.deepseek_loading = False
        
        # GUI premenné
        self.is_translating = False
        self.current_model = tk.StringVar(value="gpt-oss")
        self.translation_count = 0
        self.total_time = 0.0
        
        self.setup_gui()
        self.check_models_status()
    
    def setup_gui(self):
        """Nastavenie grafického rozhrania"""
        
        # Hlavný nadpis
        title_label = tk.Label(
            self.root, 
            text="🤖 Multi-Model Translator", 
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # Model selection panel
        model_frame = tk.Frame(self.root, bg='#f0f0f0')
        model_frame.pack(fill='x', padx=20, pady=5)
        
        model_label = tk.Label(
            model_frame,
            text="🔧 Výber modelu:",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        model_label.pack(side='left', padx=(0, 10))
        
        # Radio buttons pre výber modelu
        gpt_radio = tk.Radiobutton(
            model_frame,
            text="GPT OSS (Ollama)",
            variable=self.current_model,
            value="gpt-oss",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#2c3e50',
            command=self.on_model_change
        )
        gpt_radio.pack(side='left', padx=(0, 20))
        
        deepseek_radio = tk.Radiobutton(
            model_frame,
            text="DeepSeek-7B",
            variable=self.current_model,
            value="deepseek",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#2c3e50',
            command=self.on_model_change
        )
        deepseek_radio.pack(side='left', padx=(0, 20))
        
        # Status panel
        self.status_frame = tk.Frame(self.root, bg='#f0f0f0')
        self.status_frame.pack(fill='x', padx=20, pady=5)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="🔍 Kontrolujem modely...",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.status_label.pack(side='left')
        
        # Štatistiky panel
        stats_frame = tk.Frame(self.root, bg='#f0f0f0')
        stats_frame.pack(fill='x', padx=20, pady=5)
        
        self.stats_label = tk.Label(
            stats_frame,
            text="📊 Preklady: 0 | Celkový čas: 0.0s | Priemerný čas: 0.0s",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#34495e'
        )
        self.stats_label.pack(side='left')
        
        # Hlavný frame pre preklad
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Ľavá strana - vstupný text
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        input_label = tk.Label(
            left_frame,
            text="📝 Vstupný text (angličtina):",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        input_label.pack(anchor='w', pady=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(
            left_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg='white',
            fg='#2c3e50',
            insertbackground='#3498db'
        )
        self.input_text.pack(fill='both', expand=True)
        
        # Stredný panel s tlačidlami
        middle_frame = tk.Frame(main_frame, bg='#f0f0f0', width=140)
        middle_frame.pack(side='left', fill='y', padx=10)
        middle_frame.pack_propagate(False)
        
        # Výber cieľového jazyka
        lang_label = tk.Label(
            middle_frame,
            text="🌍 Cieľový jazyk:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        lang_label.pack(pady=(20, 5))
        
        self.target_language = tk.StringVar(value="slovenčina")
        language_combo = ttk.Combobox(
            middle_frame,
            textvariable=self.target_language,
            values=["slovenčina", "čeština", "nemčina", "francúzština", "španielčina", "taliančina"],
            state="readonly",
            width=14
        )
        language_combo.pack(pady=(0, 20))
        
        # Tlačidlo pre preklad
        self.translate_button = tk.Button(
            middle_frame,
            text="🔄\nPreložiť",
            font=("Arial", 12, "bold"),
            bg='#3498db',
            fg='white',
            activebackground='#2980b9',
            activeforeground='white',
            relief='flat',
            width=12,
            height=3,
            command=self.start_translation
        )
        self.translate_button.pack(pady=10)
        
        # Tlačidlo pre načítanie DeepSeek
        self.load_deepseek_button = tk.Button(
            middle_frame,
            text="⬇️\nNačítať\nDeepSeek",
            font=("Arial", 9),
            bg='#9b59b6',
            fg='white',
            activebackground='#8e44ad',
            activeforeground='white',
            relief='flat',
            width=12,
            height=3,
            command=self.load_deepseek_model
        )
        self.load_deepseek_button.pack(pady=5)
        
        # Tlačidlo pre vymazanie
        clear_button = tk.Button(
            middle_frame,
            text="🗑️\nVymazať",
            font=("Arial", 10),
            bg='#e74c3c',
            fg='white',
            activebackground='#c0392b',
            activeforeground='white',
            relief='flat',
            width=12,
            height=2,
            command=self.clear_texts
        )
        clear_button.pack(pady=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(
            middle_frame,
            mode='indeterminate',
            length=120
        )
        self.progress.pack(pady=10)
        
        # Pravá strana - výstupný text
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        output_label = tk.Label(
            right_frame,
            text="🔄 Preložený text:",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        output_label.pack(anchor='w', pady=(0, 5))
        
        self.output_text = scrolledtext.ScrolledText(
            right_frame,
            height=15,
            width=35,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg='#f8f9fa',
            fg='#2c3e50',
            state='disabled'
        )
        self.output_text.pack(fill='both', expand=True)
        
        # Spodný panel s informáciami
        bottom_frame = tk.Frame(self.root, bg='#f0f0f0')
        bottom_frame.pack(fill='x', padx=20, pady=10)
        
        self.info_label = tk.Label(
            bottom_frame,
            text="💡 Tip: Vyberte model, vložte text a stlačte 'Preložiť'. DeepSeek model sa načíta pri prvom použití.",
            font=("Arial", 9),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.info_label.pack()
    
    def on_model_change(self):
        """Reakcia na zmenu modelu"""
        model = self.current_model.get()
        if model == "deepseek" and not self.deepseek_model and not self.deepseek_loading:
            self.info_label.config(
                text="⚠️ DeepSeek model nie je načítaný. Stlačte 'Načítať DeepSeek' alebo použite pri preklade."
            )
        elif model == "gpt-oss":
            self.info_label.config(
                text="✅ GPT OSS model je pripravený cez Ollama."
            )
    
    def check_models_status(self):
        """Kontrola stavu modelov"""
        def check():
            # Kontrola GPT OSS
            gpt_available = False
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    gpt_available = any(model['name'] == self.gpt_model_name for model in models)
            except:
                pass
            
            # Kontrola DeepSeek závislostí
            deepseek_deps = DEEPSEEK_AVAILABLE
            
            # Aktualizácia GUI
            def update_status():
                status_parts = []
                if gpt_available:
                    status_parts.append("✅ GPT OSS")
                else:
                    status_parts.append("❌ GPT OSS")
                
                if deepseek_deps:
                    status_parts.append("✅ DeepSeek deps")
                else:
                    status_parts.append("❌ DeepSeek deps")
                
                self.status_label.config(text=" | ".join(status_parts))
                
                # Aktivácia/deaktivácia tlačidiel
                if gpt_available or deepseek_deps:
                    self.translate_button.config(state='normal')
                else:
                    self.translate_button.config(state='disabled')
                
                if not deepseek_deps:
                    self.load_deepseek_button.config(state='disabled', text="❌\nDeepSeek\nN/A")
            
            self.root.after(0, update_status)
        
        threading.Thread(target=check, daemon=True).start()
    
    def load_deepseek_model(self):
        """Načítanie DeepSeek modelu"""
        if self.deepseek_loading or self.deepseek_model:
            return
        
        if not DEEPSEEK_AVAILABLE:
            messagebox.showerror("Chyba", "DeepSeek závislosti nie sú nainštalované!")
            return
        
        self.deepseek_loading = True
        self.load_deepseek_button.config(state='disabled', text="⏳\nNačítavam...")
        self.progress.start()
        
        def load():
            try:
                self.info_label.config(text="⏳ Načítavam DeepSeek model... (môže trvať niekoľko minút)")
                
                # Načítanie tokenizera
                self.deepseek_tokenizer = AutoTokenizer.from_pretrained("deepseek-ai/deepseek-coder-7b-base")
                
                # Načítanie modelu s kvantizáciou
                self.deepseek_model = AutoModelForCausalLM.from_pretrained(
                    "deepseek-ai/deepseek-coder-7b-base",
                    device_map="auto",
                    load_in_8bit=True,
                    trust_remote_code=True
                )
                
                def success():
                    self.deepseek_loading = False
                    self.load_deepseek_button.config(
                        state='normal', 
                        text="✅\nDeepSeek\nNačítaný",
                        bg='#27ae60'
                    )
                    self.progress.stop()
                    self.info_label.config(text="✅ DeepSeek model úspešne načítaný!")
                
                self.root.after(0, success)
                
            except Exception as e:
                def error():
                    self.deepseek_loading = False
                    self.load_deepseek_button.config(state='normal', text="❌\nChyba\nDeepSeek")
                    self.progress.stop()
                    self.info_label.config(text=f"❌ Chyba pri načítaní DeepSeek: {str(e)}")
                
                self.root.after(0, error)
        
        threading.Thread(target=load, daemon=True).start()
    
    def start_translation(self):
        """Spustenie prekladu"""
        if self.is_translating:
            return
        
        input_text = self.input_text.get("1.0", tk.END).strip()
        if not input_text:
            messagebox.showwarning("Upozornenie", "Prosím, vložte text na preklad.")
            return
        
        model = self.current_model.get()
        
        # Kontrola dostupnosti vybraného modelu
        if model == "deepseek" and not self.deepseek_model:
            response = messagebox.askyesno(
                "DeepSeek nie je načítaný", 
                "DeepSeek model nie je načítaný. Chcete ho načítať teraz?"
            )
            if response:
                self.load_deepseek_model()
            return
        
        self.is_translating = True
        self.translate_button.config(state='disabled', text="⏳\nPrekladám...")
        self.progress.start()
        
        # Spustenie prekladu v novom vlákne
        thread = threading.Thread(
            target=self.translate_text,
            args=(input_text, self.target_language.get(), model),
            daemon=True
        )
        thread.start()
    
    def translate_text(self, text: str, target_lang: str, model: str):
        """Preklad textu pomocou vybraného modelu"""
        start_time = time.time()
        
        try:
            if model == "gpt-oss":
                result = self.translate_with_gpt_oss(text, target_lang)
            elif model == "deepseek":
                result = self.translate_with_deepseek(text, target_lang)
            else:
                result = None
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result:
                self.translation_count += 1
                self.total_time += duration
                self.update_output(result, duration, model)
                self.update_stats()
            else:
                self.update_output("❌ Preklad nebol úspešný", duration, model)
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            self.update_output(f"❌ Chyba: {str(e)}", duration, model)
        
        finally:
            self.root.after(0, self.translation_finished)
    
    def translate_with_gpt_oss(self, text: str, target_lang: str) -> Optional[str]:
        """Preklad pomocou GPT OSS cez Ollama"""
        prompt = f"Translate the following text to {target_lang}. Respond only with the translation:\n\n{text}\n\nTranslation:"
        
        payload = {
            "model": self.gpt_model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,
                "num_predict": 200,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1
            }
        }
        
        response = requests.post(self.ollama_url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '').strip()
        
        return None
    
    def translate_with_deepseek(self, text: str, target_lang: str) -> Optional[str]:
        """Preklad pomocou DeepSeek modelu"""
        if not self.deepseek_model or not self.deepseek_tokenizer:
            return None
        
        prompt = f"Translate the following English text to {target_lang}:\n\nEnglish: {text}\n{target_lang.capitalize()}:"
        
        inputs = self.deepseek_tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.deepseek_model.generate(
                inputs.input_ids,
                max_length=inputs.input_ids.shape[1] + 100,
                temperature=0.3,
                do_sample=True,
                pad_token_id=self.deepseek_tokenizer.eos_token_id
            )
        
        response = self.deepseek_tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extrakcia prekladu z odpovede
        if f"{target_lang.capitalize()}:" in response:
            translation = response.split(f"{target_lang.capitalize()}:")[-1].strip()
            return translation
        
        return response.replace(prompt, "").strip()
    
    def update_output(self, text: str, duration: float, model: str):
        """Aktualizácia výstupného textu"""
        def update():
            self.output_text.config(state='normal')
            self.output_text.delete("1.0", tk.END)
            self.output_text.insert("1.0", text)
            self.output_text.config(state='disabled')
            
            model_name = "GPT OSS" if model == "gpt-oss" else "DeepSeek"
            self.info_label.config(
                text=f"✅ Preklad dokončený ({model_name}) za {duration:.1f} sekúnd"
            )
        
        self.root.after(0, update)
    
    def update_stats(self):
        """Aktualizácia štatistík"""
        avg_time = self.total_time / self.translation_count if self.translation_count > 0 else 0
        
        self.stats_label.config(
            text=f"📊 Preklady: {self.translation_count} | Celkový čas: {self.total_time:.1f}s | Priemerný čas: {avg_time:.1f}s"
        )
    
    def translation_finished(self):
        """Dokončenie prekladu"""
        self.is_translating = False
        self.translate_button.config(state='normal', text="🔄\nPreložiť")
        self.progress.stop()
    
    def clear_texts(self):
        """Vymazanie textov"""
        self.input_text.delete("1.0", tk.END)
        self.output_text.config(state='normal')
        self.output_text.delete("1.0", tk.END)
        self.output_text.config(state='disabled')
        self.info_label.config(
            text="💡 Tip: Vyberte model, vložte text a stlačte 'Preložiť'."
        )


def main():
    """Hlavná funkcia"""
    root = tk.Tk()
    app = MultiModelTranslator(root)
    
    # Spustenie aplikácie
    root.mainloop()


if __name__ == "__main__":
    main()
