#!/usr/bin/env python3
"""
GPT OSS Translator - Prekladač titulkov pomocou lokálneho GPT OSS modelu cez Ollama
"""

import requests
import json
import time
from typing import Optional, Dict, Any


class GPTOSSTranslator:
    """Trieda pre preklad titulkov pomocou GPT OSS modelu cez Ollama API"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "gpt-oss:20b"):
        """
        Inicializácia prekladača
        
        Args:
            base_url: URL Ollama servera (default: http://localhost:11434)
            model: Názov modelu (default: gpt-oss:20b)
        """
        self.base_url = base_url
        self.model = model
        self.api_url = f"{base_url}/api/generate"
        
    def check_server_status(self) -> bool:
        """
        Kontrola, či je Ollama server dostupný
        
        Returns:
            bool: True ak je server dostupný, False inak
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def check_model_availability(self) -> bool:
        """
        Kontrola, či je model dostupný
        
        Returns:
            bool: True ak je model dostupný, False inak
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return any(model['name'] == self.model for model in models)
            return False
        except requests.exceptions.RequestException:
            return False
    
    def translate_text(self, text: str, target_language: str = "slovenčina", 
                      temperature: float = 0.3, max_tokens: int = 150) -> Optional[str]:
        """
        Preloží text do cieľového jazyka
        
        Args:
            text: Text na preklad
            target_language: Cieľový jazyk (default: slovenčina)
            temperature: Teplota pre generovanie (0.0-1.0, nižšie = konzistentnejšie)
            max_tokens: Maximálny počet tokenov v odpovedi
            
        Returns:
            str: Preložený text alebo None pri chybe
        """
        if not text.strip():
            return ""
        
        # Vytvorenie promptu pre preklad
        prompt = f"Translate the following text to {target_language}. Respond only with the translation:\n\n{text}\n\nTranslation:"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": temperature,
                "num_predict": max_tokens,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "stop": ["\n\n", "Translation:", "Preklad:"]
            }
        }
        
        try:
            start_time = time.time()
            response = requests.post(self.api_url, json=payload, timeout=120)  # Zvýšený timeout na 2 minúty
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                translated_text = result.get('response', '').strip()
                
                # Výpis informácií o preklade
                print(f"⏱️  Čas prekladu: {end_time - start_time:.2f}s")
                print(f"📝 Originál: {text}")
                print(f"🔄 Preklad: {translated_text}")
                print("-" * 50)
                
                return translated_text
            else:
                print(f"❌ Chyba API: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print("⏰ Timeout - preklad trval príliš dlho")
            return None
        except requests.exceptions.RequestException as e:
            print(f"🔌 Chyba pripojenia: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"📄 Chyba pri parsovaní JSON: {e}")
            return None
    
    def translate_subtitle(self, subtitle: str) -> Optional[str]:
        """
        Preloží titulok do slovenčiny s optimalizáciou pre krátke texty
        
        Args:
            subtitle: Titulok na preklad
            
        Returns:
            str: Preložený titulok alebo None pri chybe
        """
        return self.translate_text(
            subtitle, 
            target_language="slovenčina",
            temperature=0.2,  # Nižšia teplota pre konzistentnejšie preklady
            max_tokens=100    # Kratšie odpovede pre titulky
        )
    
    def batch_translate(self, texts: list, target_language: str = "slovenčina") -> Dict[str, Optional[str]]:
        """
        Preloží viacero textov naraz
        
        Args:
            texts: Zoznam textov na preklad
            target_language: Cieľový jazyk
            
        Returns:
            dict: Slovník s originálnymi textmi ako kľúčmi a prekladmi ako hodnotami
        """
        results = {}
        total = len(texts)
        
        print(f"🚀 Začínam preklad {total} textov...")
        
        for i, text in enumerate(texts, 1):
            print(f"📋 Prekladám {i}/{total}...")
            translated = self.translate_text(text, target_language)
            results[text] = translated
            
            # Krátka pauza medzi prekladmi
            if i < total:
                time.sleep(0.5)
        
        print("✅ Preklad dokončený!")
        return results


def main():
    """Hlavná funkcia pre testovanie prekladača"""
    
    print("🤖 GPT OSS Translator - Inicializácia...")
    translator = GPTOSSTranslator()
    
    # Kontrola servera
    print("🔍 Kontrolujem Ollama server...")
    if not translator.check_server_status():
        print("❌ Ollama server nie je dostupný. Uistite sa, že beží na http://localhost:11434")
        return
    
    print("✅ Ollama server je dostupný")
    
    # Kontrola modelu
    print("🔍 Kontrolujem dostupnosť modelu gpt-oss:20b...")
    if not translator.check_model_availability():
        print("❌ Model gpt-oss:20b nie je dostupný. Uistite sa, že je stiahnutý.")
        print("💡 Spustite: ollama pull gpt-oss:20b")
        return
    
    print("✅ Model gpt-oss:20b je dostupný")
    
    # Testovanie prekladu
    print("\n🧪 Testovanie prekladu...")
    
    test_texts = [
        "Hello, how are you?",
        "Good morning, have a nice day!",
        "Thank you for your help.",
        "The weather is beautiful today.",
        "I love programming in Python."
    ]
    
    print("\n📝 Jednotlivé preklady:")
    for text in test_texts:
        translated = translator.translate_subtitle(text)
        if translated:
            print(f"✅ '{text}' → '{translated}'")
        else:
            print(f"❌ Nepodarilo sa preložiť: '{text}'")
    
    print("\n📦 Hromadný preklad:")
    batch_results = translator.batch_translate(test_texts)
    
    print("\n📊 Výsledky hromadného prekladu:")
    for original, translated in batch_results.items():
        status = "✅" if translated else "❌"
        print(f"{status} '{original}' → '{translated or 'CHYBA'}'")


if __name__ == "__main__":
    main()
