#!/bin/bash

# 🤖 GPT OSS Translator - Spúš<PERSON><PERSON><PERSON> skript

echo "🤖 GPT OSS Translator"
echo "===================="

# Kontrola, či beží Ollama server
echo "🔍 Kontrolujem Ollama server..."
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "❌ Ollama server nie je spustený!"
    echo "💡 Spúšťam Ollama server..."
    
    # Spustenie Ollama servera na pozadí
    ollama serve &
    OLLAMA_PID=$!
    
    echo "⏳ Čakám na spustenie servera..."
    sleep 5
    
    # Kontrola, či sa server spustil
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "✅ Ollama server je spustený"
    else
        echo "❌ Nepodarilo sa spustiť Ollama server"
        exit 1
    fi
else
    echo "✅ Ollama server už beží"
fi

# Kontrola modelu
echo "🔍 Kontrolujem model gpt-oss:20b..."
if ollama list | grep -q "gpt-oss:20b"; then
    echo "✅ Model gpt-oss:20b je dostupný"
else
    echo "❌ Model gpt-oss:20b nie je nainštalovaný!"
    echo "💡 Chcete ho stiahnuť? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "⏬ Sťahujem model (môže trvať niekoľko minút)..."
        ollama pull gpt-oss:20b
        if [ $? -eq 0 ]; then
            echo "✅ Model úspešne stiahnutý"
        else
            echo "❌ Chyba pri sťahovaní modelu"
            exit 1
        fi
    else
        echo "❌ Bez modelu nemôže aplikácia fungovať"
        exit 1
    fi
fi

echo ""
echo "🚀 Všetko je pripravené!"
echo "📱 Vyberte aplikáciu:"
echo "  1. GUI aplikácia (odporúčané)"
echo "  2. Konzolová aplikácia"
echo "  3. Rýchly test"
echo ""

read -p "👆 Voľba (1-3): " choice

case $choice in
    1)
        echo "🎨 Spúšťam GUI aplikáciu..."
        python3 translator_app.py
        ;;
    2)
        echo "💻 Spúšťam konzolovou aplikáciu..."
        python3 simple_translator.py
        ;;
    3)
        echo "🧪 Spúšťam rýchly test..."
        python3 quick_test.py
        ;;
    *)
        echo "❌ Neplatná voľba, spúšťam GUI aplikáciu..."
        python3 translator_app.py
        ;;
esac

echo ""
echo "👋 Ďakujem za použitie GPT OSS Translator!"
